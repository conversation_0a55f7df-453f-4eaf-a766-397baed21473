
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CreditCard, Banknote, Smartphone, Receipt } from 'lucide-react';
import { Order } from '../types/pos';
import { formatCurrency } from '../utils/posData';
import { useToast } from '../hooks/use-toast';
import Navigation from '../components/Navigation';

const Cashier = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'qr'>('cash');
  const [cashReceived, setCashReceived] = useState<string>('');
  const navigate = useNavigate();
  const { toast } = useToast();

  const fetchOrders = () => {
    fetch('/api/orders')
      .then(res => res.json())
      .then(data => {
        setOrders(data.map((order: any) => ({
          ...order,
          createdAt: new Date(order.createdAt),
          updatedAt: new Date(order.updatedAt)
        })));
      });
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  const handlePayment = async () => {
    if (!selectedOrder) return;

    const receivedAmount = parseFloat(cashReceived);
    if (paymentMethod === 'cash' && receivedAmount < selectedOrder.total) {
      toast({
        title: "Pembayaran tidak cukup",
        description: "Jumlah uang yang diterima kurang dari total tagihan",
        variant: "destructive"
      });
      return;
    }

    // Update order status to completed in the database
    try {
      await fetch(`/api/orders/${selectedOrder.id}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'completed' })
      });
      toast({
        title: "Pembayaran berhasil!",
        description: `Pesanan meja ${selectedOrder.tableId} telah dibayar`,
      });
      fetchOrders();
      setSelectedOrder(null);
      setCashReceived('');
      // Redirect ke halaman struk
      navigate(`/receipt/${selectedOrder.id}`);
    } catch (err) {
      toast({
        title: 'Gagal update status pembayaran',
        description: String(err),
        variant: 'destructive'
      });
    }
  };

  const getChange = () => {
    if (!selectedOrder || paymentMethod !== 'cash') return 0;
    const received = parseFloat(cashReceived) || 0;
    return Math.max(0, received - selectedOrder.total);
  };

  const readyToPayOrders = orders.filter(order => order.status === 'served');

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-6 py-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Kasir</h1>
          <p className="text-gray-600">Proses pembayaran dan cetak struk</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order List */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold text-gray-800">Pesanan Siap Bayar</h3>
            </div>
            <div className="p-6 space-y-4">
              {readyToPayOrders.map(order => (
                <div
                  key={order.id}
                  onClick={() => setSelectedOrder(order)}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    selectedOrder?.id === order.id
                      ? 'border-primary bg-primary/5'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">Meja {order.tableId}</h4>
                      <p className="text-sm text-gray-600">
                        {(order.orderItems || []).length} item
                      </p>
                    </div>
                    <span className="font-bold text-lg text-primary">
                      {formatCurrency(order.total)}
                    </span>
                  </div>
                  
                  <div className="space-y-1">
                    {(order.orderItems || []).map(item => (
                      <div key={item.id} className="flex justify-between text-sm">
                        <span>{item.quantity}x {item.menuItem.name}</span>
                        <span>{formatCurrency(item.menuItem.price * item.quantity)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
              
              {readyToPayOrders.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Receipt className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Tidak ada pesanan yang siap untuk dibayar</p>
                </div>
              )}
            </div>
          </div>

          {/* Payment Section */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold text-gray-800">Pembayaran</h3>
            </div>
            <div className="p-6">
              {selectedOrder ? (
                <div className="space-y-6">
                  {/* Order Summary */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium mb-2">Meja {selectedOrder.tableId}</h4>
                    <div className="space-y-1">
                      {(selectedOrder.orderItems || []).map(item => (
                        <div key={item.id} className="flex justify-between text-sm">
                          <span>{item.quantity}x {item.menuItem.name}</span>
                          <span>{formatCurrency(item.menuItem.price * item.quantity)}</span>
                        </div>
                      ))}
                    </div>
                    <div className="border-t pt-2 mt-2">
                      <div className="flex justify-between font-bold">
                        <span>Total:</span>
                        <span>{formatCurrency(selectedOrder.total)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div>
                    <h4 className="font-medium mb-3">Metode Pembayaran</h4>
                    <div className="grid grid-cols-3 gap-3">
                      <button
                        onClick={() => setPaymentMethod('cash')}
                        className={`p-3 rounded-lg border-2 transition-all ${
                          paymentMethod === 'cash'
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Banknote className="w-6 h-6 mx-auto mb-1" />
                        <span className="text-sm">Tunai</span>
                      </button>
                      <button
                        onClick={() => setPaymentMethod('card')}
                        className={`p-3 rounded-lg border-2 transition-all ${
                          paymentMethod === 'card'
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <CreditCard className="w-6 h-6 mx-auto mb-1" />
                        <span className="text-sm">Kartu</span>
                      </button>
                      <button
                        onClick={() => setPaymentMethod('qr')}
                        className={`p-3 rounded-lg border-2 transition-all ${
                          paymentMethod === 'qr'
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Smartphone className="w-6 h-6 mx-auto mb-1" />
                        <span className="text-sm">QR Code</span>
                      </button>
                    </div>
                  </div>

                  {/* Cash Input */}
                  {paymentMethod === 'cash' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Uang Diterima
                      </label>
                      <input
                        type="number"
                        value={cashReceived}
                        onChange={(e) => setCashReceived(e.target.value)}
                        placeholder="Masukkan jumlah uang"
                        className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                      {cashReceived && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <div className="flex justify-between">
                            <span>Kembalian:</span>
                            <span className="font-bold">{formatCurrency(getChange())}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Payment Button */}
                  <button
                    onClick={handlePayment}
                    disabled={paymentMethod === 'cash' && (!cashReceived || parseFloat(cashReceived) < selectedOrder.total)}
                    className="w-full bg-primary text-primary-foreground py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Proses Pembayaran
                  </button>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <CreditCard className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Pilih pesanan untuk memulai pembayaran</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cashier;
