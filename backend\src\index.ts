import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';

// Load environment variables
dotenv.config();

const app = express();
const prisma = new PrismaClient();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Restaurant POS API is running' });
});

// Menu Categories API
app.get('/api/categories', async (req, res) => {
  try {
    const categories = await prisma.menuCategory.findMany({
      include: {
        menuItems: true
      }
    });
    res.json(categories);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

app.post('/api/categories', async (req, res) => {
  try {
    const { name, icon, color } = req.body;
    const category = await prisma.menuCategory.create({
      data: { name, icon, color }
    });
    res.status(201).json(category);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create category' });
  }
});

// Menu Items API
app.get('/api/menu-items', async (req, res) => {
  try {
    const { category, available } = req.query;
    const where: any = {};
    
    if (category) where.categoryId = category;
    if (available !== undefined) where.available = available === 'true';
    
    const menuItems = await prisma.menuItem.findMany({
      where,
      include: {
        category: true
      }
    });
    res.json(menuItems);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch menu items' });
  }
});

app.post('/api/menu-items', async (req, res) => {
  try {
    const { name, description, price, categoryId, image, available, preparationTime, ingredients } = req.body;
    const menuItem = await prisma.menuItem.create({
      data: {
        name,
        description,
        price: parseFloat(price),
        categoryId,
        image,
        available: available ?? true,
        preparationTime: preparationTime ? parseInt(preparationTime) : null,
        ingredients: ingredients ? JSON.stringify(ingredients) : null
      },
      include: {
        category: true
      }
    });
    res.status(201).json(menuItem);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create menu item' });
  }
});

app.put('/api/menu-items/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, price, categoryId, image, available, preparationTime, ingredients } = req.body;
    const menuItem = await prisma.menuItem.update({
      where: { id },
      data: {
        name,
        description,
        price: parseFloat(price),
        categoryId,
        image,
        available,
        preparationTime: preparationTime ? parseInt(preparationTime) : null,
        ingredients: ingredients ? JSON.stringify(ingredients) : null
      },
      include: {
        category: true
      }
    });
    res.json(menuItem);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update menu item' });
  }
});

app.delete('/api/menu-items/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await prisma.menuItem.delete({
      where: { id }
    });
    res.json({ message: 'Menu item deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete menu item' });
  }
});

// Bulk import menu items
app.post('/api/menu-items/bulk-import', async (req, res) => {
  try {
    const { menuItems } = req.body;

    if (!Array.isArray(menuItems)) {
      return res.status(400).json({ error: 'menuItems must be an array' });
    }

    const results = [];
    const errors = [];

    for (let i = 0; i < menuItems.length; i++) {
      try {
        const item = menuItems[i];
        const { name, description, price, categoryId, image, available, preparationTime, ingredients } = item;

        const menuItem = await prisma.menuItem.create({
          data: {
            name,
            description,
            price: parseFloat(price),
            categoryId,
            image,
            available: available ?? true,
            preparationTime: preparationTime ? parseInt(preparationTime) : null,
            ingredients: ingredients ? JSON.stringify(ingredients) : null
          },
          include: {
            category: true
          }
        });

        results.push(menuItem);
      } catch (error: any) {
        errors.push({ index: i, error: error.message });
      }
    }

    res.json({
      success: true,
      imported: results.length,
      errors: errors.length,
      results,
      errors
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to bulk import menu items' });
  }
});

// Tables API
app.get('/api/tables', async (req, res) => {
  try {
    const tables = await prisma.table.findMany({
      include: {
        orders: {
          where: {
            status: {
              in: ['pending', 'preparing', 'ready', 'served']
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            orderItems: {
              include: {
                menuItem: true
              }
            }
          }
        }
      }
    });
    // Kembalikan array orders aktif, bukan hanya currentOrder
    res.json(tables);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch tables' });
  }
});

app.post('/api/tables', async (req, res) => {
  try {
    const { number, seats, status, reservationTime } = req.body;
    const table = await prisma.table.create({
      data: { number, seats, status, reservationTime }
    });
    res.status(201).json(table);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create table' });
  }
});

// Edit table
app.put('/api/tables/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { number, seats, status, reservationTime } = req.body;
    const table = await prisma.table.update({
      where: { id },
      data: { number, seats, status, reservationTime }
    });
    res.json(table);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update table' });
  }
});

// Delete table
app.delete('/api/tables/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await prisma.table.delete({ where: { id } });
    res.json({ message: 'Table deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete table' });
  }
});

// Orders API
app.get('/api/orders', async (req, res) => {
  try {
    const { status, tableId } = req.query;
    const where: any = {};
    
    if (status) where.status = status;
    if (tableId) where.tableId = tableId;
    
    const orders = await prisma.order.findMany({
      where,
      include: {
        table: true,
        customer: true,
        orderItems: {
          include: {
            menuItem: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch orders' });
  }
});

// Customers API
app.get('/api/customers', async (req, res) => {
  try {
    const { search } = req.query;
    const where: any = {};
    if (search) {
      where.name = { contains: search };
    }
    const customers = await prisma.customer.findMany({
      where,
      orderBy: { name: 'asc' },
      take: 10,
    });
    res.json(customers);
  } catch (error) {
    console.error(error); // Log the error for debugging
    res.status(500).json({ error: 'Failed to fetch customers' });
  }
});

app.post('/api/customers', async (req, res) => {
  try {
    const { name, phone } = req.body;
    const customer = await prisma.customer.create({ data: { name, phone } });
    res.status(201).json(customer);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create customer' });
  }
});

// Update POST /api/orders to accept customerId or customer data
app.post('/api/orders', async (req, res) => {
  try {
    const { tableId, items, total, customerId, customer, createdBy } = req.body;
    let finalTableId = tableId;
    // Untuk online-order dan takeaway, selalu buat table baru
    if (tableId === 'online-order' || tableId === 'takeaway') {
      const { customAlphabet } = require('nanoid');
      const nanoid = customAlphabet('1234567890abcdef', 10);
      const newTableId = `${tableId}-${nanoid()}`;
      const newTable = await prisma.table.create({
        data: {
          id: newTableId,
          number: Math.floor(Math.random() * 10000) + 1000, // nomor acak
          seats: 0,
          status: 'occupied',
        }
      });
      finalTableId = newTable.id;
    }
    // Validate tableId (except for online/takeaway which is now handled)
    const table = await prisma.table.findUnique({ where: { id: finalTableId } });
    if (!table) {
      return res.status(400).json({ error: 'Invalid tableId' });
    }
    // Validate customerId if provided
    let usedCustomerId = customerId;
    if (usedCustomerId) {
      const customerExists = await prisma.customer.findUnique({ where: { id: usedCustomerId } });
      if (!customerExists) {
        return res.status(400).json({ error: 'Invalid customerId' });
      }
    } else if (customer && customer.name) {
      // Create new customer if not exists
      const created = await prisma.customer.create({ data: { name: customer.name, phone: customer.phone } });
      usedCustomerId = created.id;
    }
    // Validate all menuItemIds
    for (const item of items as Array<{ menuItemId: string }>) {
      const menuItem = await prisma.menuItem.findUnique({ where: { id: item.menuItemId } });
      if (!menuItem) {
        return res.status(400).json({ error: `Invalid menuItemId: ${item.menuItemId}` });
      }
    }
    // Generate unique noStruk (e.g., STRUK-YYYYMMDD-XXXX)
    const now = new Date();
    const dateStr = now.toISOString().slice(0,10).replace(/-/g, '');
    const randomNum = Math.floor(1000 + Math.random() * 9000);
    const noStruk = `STRUK-${dateStr}-${randomNum}`;
    const order = await prisma.order.create({
      data: {
        tableId: finalTableId,
        total: parseFloat(total),
        customerId: usedCustomerId,
        status: 'pending',
        createdBy: createdBy || null,
        noStruk,
        orderItems: {
          create: (items as Array<{ menuItemId: string; quantity: number; notes?: string; price: number }>).map((item) => ({
            menuItemId: item.menuItemId,
            quantity: item.quantity,
            notes: item.notes,
            price: item.price
          }))
        }
      } as any, // Cast to any to avoid linter error
      include: {
        table: true,
        orderItems: {
          include: {
            menuItem: true
          }
        },
        customer: true
      }
    });
    // Stock out otomatis untuk setiap bahan menu
    for (const item of items) {
      const menuItem = await prisma.menuItem.findUnique({ where: { id: item.menuItemId } });
      if (!menuItem || !menuItem.ingredients) continue;
      let ingredientsArr;
      try {
        ingredientsArr = Array.isArray(menuItem.ingredients)
          ? menuItem.ingredients
          : JSON.parse(menuItem.ingredients);
      } catch {
        ingredientsArr = [];
      }
      for (const bahan of ingredientsArr) {
        // Support both string and object format
        let bahanName, bahanQty;
        if (typeof bahan === 'string') {
          bahanName = bahan;
          bahanQty = 1;
        } else {
          bahanName = bahan.name;
          bahanQty = Number(bahan.qty) || 1;
        }
        const stockItem = await prisma.stockItem.findFirst({ where: { name: bahanName } });
        if (!stockItem) continue;
        const outAmount = bahanQty * Number(item.quantity);
        let newStock = stockItem.currentStock - outAmount;
        if (newStock < 0) newStock = 0;
        await prisma.stockItem.update({ where: { id: stockItem.id }, data: { currentStock: newStock } });
        await prisma.stockMutation.create({
          data: {
            stockItemId: stockItem.id,
            type: 'OUT',
            amount: outAmount,
            user: createdBy || 'system',
            transactionName: 'Penjualan',
          },
        });
      }
    }
    // Update table status to occupied (only for real tables, not online/takeaway)
    if (tableId && !tableId.startsWith('online-') && !tableId.startsWith('takeaway')) {
      try {
        await prisma.table.update({
          where: { id: tableId },
          data: { status: 'occupied' }
        });
      } catch (tableError) {
        console.warn(`Failed to update table status for tableId: ${tableId}`, tableError);
        // Don't fail the entire order creation if table update fails
      }
    }
    res.status(201).json(order);
  } catch (error) {
    console.error(error); // Log error detail for debugging
    res.status(500).json({ error: 'Failed to create order' });
  }
});

app.put('/api/orders/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const order = await prisma.order.update({
      where: { id },
      data: { status },
      include: {
        table: true,
        orderItems: {
          include: {
            menuItem: true
          }
        }
      }
    });

    // If order is cancelled, restore stock
    if (status === 'cancelled') {
      for (const orderItem of order.orderItems) {
        const menuItem = orderItem.menuItem;
        if (!menuItem || !menuItem.ingredients) continue;

        let ingredientsArr;
        try {
          ingredientsArr = Array.isArray(menuItem.ingredients)
            ? menuItem.ingredients
            : JSON.parse(menuItem.ingredients);
        } catch {
          ingredientsArr = [];
        }

        for (const bahan of ingredientsArr) {
          // Support both string and object format
          let bahanName, bahanQty;
          if (typeof bahan === 'string') {
            bahanName = bahan;
            bahanQty = 1;
          } else {
            bahanName = bahan.name;
            bahanQty = Number(bahan.qty) || 1;
          }

          const stockItem = await prisma.stockItem.findFirst({ where: { name: bahanName } });
          if (!stockItem) continue;

          const restoreAmount = bahanQty * Number(orderItem.quantity);
          const newStock = stockItem.currentStock + restoreAmount;

          await prisma.stockItem.update({
            where: { id: stockItem.id },
            data: { currentStock: newStock }
          });

          await prisma.stockMutation.create({
            data: {
              stockItemId: stockItem.id,
              type: 'IN',
              amount: restoreAmount,
              user: 'system',
              transactionName: 'Pembatalan Pesanan',
            },
          });
        }
      }
    }

    // If order is completed or cancelled, make table available
    if (status === 'completed' || status === 'cancelled') {
      await prisma.table.update({
        where: { id: order.tableId },
        data: { status: 'available' }
      });
    }

    res.json(order);
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({ error: 'Failed to update order status' });
  }
});

// Users API
app.get('/api/users', async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        username: true,
        email: true,
        role: true,
        status: true,
        lastLogin: true,
        createdAt: true
      }
    });
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// User Registration API
app.post('/api/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;
    if (!username || !email || !password) {
      return res.status(400).json({ error: 'Semua field wajib diisi.' });
    }
    // Cek apakah username/email sudah ada
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email }
        ]
      }
    });
    if (existingUser) {
      return res.status(400).json({ error: 'Username atau email sudah terdaftar.' });
    }
    // Buat user baru, default role: PELAYAN
    const user = await prisma.user.create({
      data: {
        name: username,
        username,
        email,
        password, // (disarankan: hash password di produksi)
        role: 'PELAYAN',
        status: 'active'
      }
    });
    res.status(201).json({ message: 'Registrasi berhasil', user: { username: user.username, email: user.email, role: user.role } });
  } catch (error) {
    res.status(500).json({ error: 'Gagal registrasi user' });
  }
});

// Stock Items API
app.get('/api/stock', async (req, res) => {
  try {
    const stockItems = await prisma.stockItem.findMany();
    res.json(stockItems);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch stock items' });
  }
});

app.post('/api/stock', async (req, res) => {
  try {
    const { name, category, currentStock, minStock, maxStock, unit, cost, supplier } = req.body;
    const stockItem = await prisma.stockItem.create({
      data: {
        name,
        category,
        currentStock: parseFloat(currentStock),
        minStock: parseFloat(minStock),
        maxStock: parseFloat(maxStock),
        unit,
        cost: parseFloat(cost),
        supplier
      }
    });
    res.status(201).json(stockItem);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create stock item' });
  }
});

app.put('/api/stock/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, category, currentStock, minStock, maxStock, unit, cost, supplier } = req.body;
    const updated = await prisma.stockItem.update({
      where: { id },
      data: {
        name,
        category,
        currentStock: parseFloat(currentStock),
        minStock: parseFloat(minStock),
        maxStock: parseFloat(maxStock),
        unit,
        cost: parseFloat(cost),
        supplier,
      },
    });
    res.json(updated);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update stock item' });
  }
});

// Get stock mutation history for a stock item
app.get('/api/stock/:id/mutation', async (req, res) => {
  try {
    const { id } = req.params;
    const mutations = await prisma.stockMutation.findMany({
      where: { stockItemId: id },
      include: {
        stockItem: {
          select: {
            name: true,
            unit: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
    });
    res.json(mutations);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch stock mutation history' });
  }
});

// Get all stock mutations for stock history page
app.get('/api/stock-history', async (req, res) => {
  try {
    const mutations = await prisma.stockMutation.findMany({
      include: {
        stockItem: {
          select: {
            name: true,
            unit: true,
            category: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
    });
    res.json(mutations);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch stock history' });
  }
});

// Update stock mutation
app.put('/api/stock-mutation/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { type, amount, user, transactionName } = req.body;

    if (!['IN', 'OUT'].includes(type)) {
      return res.status(400).json({ error: 'Invalid mutation type' });
    }

    // Get the current mutation to calculate stock difference
    const currentMutation = await prisma.stockMutation.findUnique({
      where: { id },
      include: { stockItem: true }
    });

    if (!currentMutation) {
      return res.status(404).json({ error: 'Stock mutation not found' });
    }

    // Calculate the difference in stock change
    const oldEffect = currentMutation.type === 'IN' ? currentMutation.amount : -currentMutation.amount;
    const newEffect = type === 'IN' ? Number(amount) : -Number(amount);
    const stockDifference = newEffect - oldEffect;

    // Update stock item
    const newStock = Math.max(0, currentMutation.stockItem.currentStock + stockDifference);
    await prisma.stockItem.update({
      where: { id: currentMutation.stockItemId },
      data: { currentStock: newStock }
    });

    // Update mutation
    const updatedMutation = await prisma.stockMutation.update({
      where: { id },
      data: {
        type,
        amount: Number(amount),
        user,
        transactionName: transactionName || null,
      },
      include: {
        stockItem: {
          select: {
            name: true,
            unit: true,
            category: true
          }
        }
      }
    });

    res.json(updatedMutation);
  } catch (error) {
    console.error('Error updating stock mutation:', error);
    res.status(500).json({ error: 'Failed to update stock mutation' });
  }
});

// Delete stock mutation
app.delete('/api/stock-mutation/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Get the mutation to reverse its effect on stock
    const mutation = await prisma.stockMutation.findUnique({
      where: { id },
      include: { stockItem: true }
    });

    if (!mutation) {
      return res.status(404).json({ error: 'Stock mutation not found' });
    }

    // Reverse the stock effect
    const stockEffect = mutation.type === 'IN' ? -mutation.amount : mutation.amount;
    const newStock = Math.max(0, mutation.stockItem.currentStock + stockEffect);

    await prisma.stockItem.update({
      where: { id: mutation.stockItemId },
      data: { currentStock: newStock }
    });

    // Delete the mutation
    await prisma.stockMutation.delete({
      where: { id }
    });

    res.json({ message: 'Stock mutation deleted successfully' });
  } catch (error) {
    console.error('Error deleting stock mutation:', error);
    res.status(500).json({ error: 'Failed to delete stock mutation' });
  }
});

// Add stock mutation (in/out)
app.post('/api/stock/:id/mutation', async (req, res) => {
  try {
    const { id } = req.params;
    const { type, amount, user, transactionName } = req.body;
    if (!['IN', 'OUT'].includes(type)) {
      return res.status(400).json({ error: 'Invalid mutation type' });
    }
    const stockItem = await prisma.stockItem.findUnique({ where: { id } });
    if (!stockItem) return res.status(404).json({ error: 'Stock item not found' });
    // Update stock
    let newStock = type === 'IN' ? stockItem.currentStock + Number(amount) : stockItem.currentStock - Number(amount);
    if (newStock < 0) newStock = 0;
    await prisma.stockItem.update({ where: { id }, data: { currentStock: newStock } });
    // Log mutation
    const mutation = await prisma.stockMutation.create({
      data: {
        stockItemId: id,
        type,
        amount: Number(amount),
        user,
        transactionName: transactionName || null,
      },
    });
    res.status(201).json(mutation);
  } catch (error) {
    console.error('Error in mutation endpoint:', error);
    res.status(500).json({ error: 'Failed to add stock mutation' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  process.exit(0);
}); 