import { useState } from 'react';
import { Table } from '../types/pos';
import { getTableStatusColor, getTableStatusText } from '../utils/posData';
import { Users, Clock, Eye, EyeOff } from 'lucide-react';

interface TableCardProps {
  table: Table;
  onTableClick: (table: Table) => void;
  onViewOrderDetails?: (table: Table) => void;
  onEditTable?: (table: Table) => void;
  onDeleteTable?: (table: Table) => void;
  onPayOrder?: (table: Table) => void;
}

function TableCard({ table, onTableClick, onViewOrderDetails, onEditTable, onDeleteTable, onPayOrder }: TableCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(true);

  // Helper functions for styling
  function getEnhancedStatusBadge(status: string) {
    if (status === 'available') return 'bg-gradient-to-r from-emerald-500 to-green-600 text-white border-emerald-400';
    if (status === 'occupied') return 'bg-gradient-to-r from-rose-500 to-red-600 text-white border-rose-400';
    if (status === 'reserved') return 'bg-gradient-to-r from-amber-500 to-yellow-600 text-white border-amber-400';
    return 'bg-gradient-to-r from-gray-500 to-slate-600 text-white border-gray-400';
  }

  function getTableNumberStyle(status: string) {
    if (status === 'available') return 'text-emerald-700 bg-emerald-100 border-emerald-300';
    if (status === 'occupied') return 'text-rose-700 bg-rose-100 border-rose-300';
    if (status === 'reserved') return 'text-amber-700 bg-amber-100 border-amber-300';
    return 'text-gray-700 bg-gray-100 border-gray-300';
  }

  function getTopBorderClasses(status: string) {
    if (status === 'available') return 'bg-gradient-to-r from-emerald-400 to-green-500';
    if (status === 'occupied') return 'bg-gradient-to-r from-rose-400 to-red-500';
    if (status === 'reserved') return 'bg-gradient-to-r from-amber-400 to-yellow-500';
    return 'bg-gradient-to-r from-gray-400 to-slate-500';
  }

  function getFloatingIndicatorClasses(status: string) {
    if (status === 'available') return 'bg-gradient-to-br from-emerald-400 to-green-500';
    if (status === 'occupied') return 'bg-gradient-to-br from-rose-400 to-red-500';
    if (status === 'reserved') return 'bg-gradient-to-br from-amber-400 to-yellow-500';
    return 'bg-gradient-to-br from-gray-400 to-slate-500';
  }

  function getKitchenStatusClasses(status: string) {
    if (status === 'pending') return 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-yellow-300';
    if (status === 'preparing') return 'bg-gradient-to-r from-blue-400 to-indigo-500 text-white border-blue-300';
    if (status === 'ready') return 'bg-gradient-to-r from-green-400 to-emerald-500 text-white border-green-300';
    if (status === 'served') return 'bg-gradient-to-r from-gray-400 to-slate-500 text-white border-gray-300';
    return 'bg-gradient-to-r from-gray-400 to-slate-500 text-white border-gray-300';
  }

  function getKitchenStatusText(status: string) {
    if (status === 'pending') return '⏳ Menunggu Dapur';
    if (status === 'preparing') return '👨‍🍳 Sedang Dimasak';
    if (status === 'ready') return '✅ Siap Disajikan';
    if (status === 'served') return '🍽️ Sudah Disajikan';
    return '❓ Status Tidak Diketahui';
  }

  // Build class names
  const baseClasses = 'relative overflow-hidden rounded-xl border-2 cursor-pointer transition-all duration-300';
  const statusClasses = getTableStatusColor(table.status);
  const hoverClasses = isHovered ? 'transform scale-105 shadow-xl' : 'shadow-lg';
  const glowClasses = table.status === 'occupied' ? 'ring-2 ring-blue-300 ring-opacity-50' : '';
  const cardClasses = `${baseClasses} ${statusClasses} ${hoverClasses} ${glowClasses} hover:shadow-2xl`;

  return (
    <div
      className={cardClasses}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => onTableClick(table)}
    >
      {/* Decorative top border */}
      <div className={`h-1 w-full ${getTopBorderClasses(table.status)}`} />
      
      <div className="p-4">
        <div className="flex flex-col items-center space-y-3 relative">
          {/* View/Hide Toggle Button */}
          <div className="absolute top-0 left-0 z-10">
            <button
              className="bg-white/90 hover:bg-white border border-gray-300 rounded-full p-2 shadow-md transition-all duration-200 hover:scale-110"
              onClick={(e) => {
                e.stopPropagation();
                setIsContentVisible(!isContentVisible);
              }}
              title={isContentVisible ? "Sembunyikan Detail" : "Tampilkan Detail"}
            >
              {isContentVisible ? (
                <EyeOff className="w-4 h-4 text-gray-600" />
              ) : (
                <Eye className="w-4 h-4 text-gray-600" />
              )}
            </button>
          </div>

          {/* Enhanced table number */}
          <div className={`w-16 h-16 rounded-full border-2 flex items-center justify-center text-2xl font-bold shadow-md ${getTableNumberStyle(table.status)}`}>
            {table.number}
          </div>

          {/* Enhanced seats info */}
          <div className="flex items-center text-sm font-medium text-gray-700 bg-white/70 px-3 py-1 rounded-full shadow-sm">
            <Users className="w-4 h-4 mr-1 text-gray-600" />
            <span>{table.seats} kursi</span>
          </div>

          {/* Enhanced status badge */}
          <div className={`px-4 py-2 rounded-full text-xs font-bold shadow-md border ${getEnhancedStatusBadge(table.status)}`}>
            {getTableStatusText(table.status)}
            {table.status === 'occupied' && (
              <div className="text-xs mt-1 opacity-75">
                🍽️ Meja sedang digunakan
              </div>
            )}
          </div>

          {/* Collapsible Content */}
          {isContentVisible && (
            <div className="w-full space-y-3">
              {/* Action buttons for occupied tables */}
              {table.status === 'occupied' && (
                <div className="w-full space-y-2">
                  <button
                    className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-3 py-2 rounded-xl text-xs font-bold shadow-lg transform transition-all duration-200 hover:scale-105"
                    onClick={e => { e.stopPropagation(); onViewOrderDetails && onViewOrderDetails(table); }}
                  >
                    👁️ Lihat Detail Pesanan
                  </button>
                  <button
                    className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-3 py-2 rounded-xl text-xs font-bold shadow-lg transform transition-all duration-200 hover:scale-105"
                    onClick={e => { e.stopPropagation(); onTableClick(table); }}
                  >
                    ➕ Tambah Pesanan
                  </button>
                </div>
              )}

              {/* Enhanced reservation time */}
              {table.status === 'reserved' && table.reservationTime && (
                <div className="flex items-center text-xs font-medium text-amber-700 bg-amber-100 px-3 py-2 rounded-full shadow-sm border border-amber-200">
                  <Clock className="w-4 h-4 mr-2 text-amber-600" />
                  <span>{table.reservationTime}</span>
                </div>
              )}

              {/* Order information for occupied tables */}
              {table.status === 'occupied' && Array.isArray(table.orders) && table.orders.length > 0 && (
                <div className="w-full space-y-2">
                  {table.orders.map(order => (
                    <div key={order.id} className="w-full p-3 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 shadow-lg">
                      {/* Order header */}
                      <div className="text-center mb-2">
                        <div className="text-xs font-bold text-blue-900 bg-blue-200 px-2 py-1 rounded-full inline-block">
                          Pesanan: #{order.noStruk}
                        </div>
                      </div>

                      {/* Order details */}
                      <div className="space-y-1 text-xs">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Total:</span>
                          <span className="font-bold text-green-700">Rp{order.total.toLocaleString('id-ID')}</span>
                        </div>

                        {order.createdBy && (
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Diinput oleh:</span>
                            <span className="font-semibold text-blue-700">{order.createdBy}</span>
                          </div>
                        )}

                        {order.customer && (
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Customer:</span>
                            <span className="font-semibold text-purple-700">{order.customer.name}</span>
                          </div>
                        )}
                      </div>

                      {/* Kitchen status */}
                      <div className="mt-2 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-bold shadow-sm border ${getKitchenStatusClasses(order.status)}`}>
                          {getKitchenStatusText(order.status)}
                        </span>
                      </div>

                      {/* Payment button */}
                      {onPayOrder && (
                        <button
                          className="mt-3 w-full bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-600 hover:from-emerald-600 hover:via-blue-600 hover:to-purple-700 text-white px-3 py-2 rounded-xl text-xs font-bold shadow-lg transform transition-all duration-200 hover:scale-105"
                          onClick={e => { e.stopPropagation(); onPayOrder(table); }}
                        >
                          💳 Pembayaran
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Enhanced edit/delete buttons */}
          {isHovered && (
            <div className="absolute top-3 right-3 flex gap-2">
              {onEditTable && (
                <button
                  className="bg-gradient-to-r from-amber-400 to-yellow-500 hover:from-amber-500 hover:to-yellow-600 text-white rounded-full p-2 text-xs shadow-lg transform transition-all duration-200 hover:scale-110"
                  onClick={e => { e.stopPropagation(); onEditTable(table); }}
                  title="Edit Meja"
                >
                  ✏️
                </button>
              )}
              {onDeleteTable && (
                <button
                  className="bg-gradient-to-r from-red-400 to-rose-500 hover:from-red-500 hover:to-rose-600 text-white rounded-full p-2 text-xs shadow-lg transform transition-all duration-200 hover:scale-110"
                  onClick={e => { e.stopPropagation(); onDeleteTable(table); }}
                  title="Hapus Meja"
                >
                  🗑️
                </button>
              )}
            </div>
          )}
          
          {/* Floating status indicator */}
          <div className={`absolute -top-2 -right-2 w-6 h-6 rounded-full border-2 border-white shadow-lg ${getFloatingIndicatorClasses(table.status)}`}>
            <div className="w-full h-full rounded-full flex items-center justify-center text-white text-xs font-bold">
              {table.status === 'available' ? '✓' :
               table.status === 'occupied' ? '●' :
               table.status === 'reserved' ? '⏰' : '?'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TableCard;
