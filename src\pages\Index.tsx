
import { useState, useEffect, useRef } from 'react';
import { Store, Users, TrendingUp, Clock, Settings } from 'lucide-react';
import { Table, OrderItem, UserRole } from '../types/pos';
import OrderModal from '../components/OrderModal';
import Navigation from '../components/Navigation';
import { useToast } from '../hooks/use-toast';
import TableFormModal from '../components/TableFormModal';
import ConfirmDialog from '../components/ConfirmDialog';
import { useNavigate } from 'react-router-dom';
import PaymentModal from '../components/PaymentModal';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import TableCard from '../components/TableCard';
import TableGrid from '../components/TableGrid';
import OrderDetailModal from '../components/OrderDetailModal';

interface Order {
  id: string;
  tableId: string;
  customer?: { name: string; phone?: string };
  status: string;
  total: number;
  createdAt: string;
  table: { id: string; number: number };
}

const Index = () => {
  const [tables, setTables] = useState<Table[]>([]);
  const [menuItems, setMenuItems] = useState([]);
  const [menuCategories, setMenuCategories] = useState([]);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);
  const [isTableFormOpen, setIsTableFormOpen] = useState(false);
  const [editingTable, setEditingTable] = useState<Table | null>(null);
  const [deletingTable, setDeletingTable] = useState<Table | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [payingTable, setPayingTable] = useState<Table | null>(null);
  const [loading, setLoading] = useState(true);
  const [orderType, setOrderType] = useState<'table' | 'online' | 'takeaway'>('table');
  const [tab, setTab] = useState<'layout' | 'online'>('layout');
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);

  // Ref untuk container meja agar bisa di-scroll ke atas
  const tableContainerRef = useRef<HTMLDivElement>(null);

  const userStr = localStorage.getItem('user');
  const user = userStr ? JSON.parse(userStr) : null;
  const isPelayan = user && user.role === UserRole.PELAYAN;
  const isAdmin = user && user.role === UserRole.ADMIN;

  // Fungsi untuk scroll ke atas
  const scrollToTop = () => {
    if (tableContainerRef.current) {
      tableContainerRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    } else {
      // Fallback: scroll window ke atas
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  // Fetch tables from backend
  useEffect(() => {
    const fetchTables = async () => {
      setLoading(true);
      try {
        const res = await fetch('/api/tables');
        const data = await res.json();
        // Set status meja berdasarkan ada/tidaknya pesanan aktif
        const mappedTables = data.map((table: any) => ({
          ...table,
          status: Array.isArray(table.orders) && table.orders.length > 0 ? 'occupied' : 'available',
        }));
        setTables(mappedTables);

        // Scroll ke atas setelah data meja berhasil dimuat
        // Hanya scroll pada refresh otomatis, bukan pada load pertama
        if (!loading) {
          scrollToTop();
        }
      } catch (err) {
        toast({ title: 'Gagal memuat data meja', description: String(err), variant: 'destructive' });
      } finally {
        setLoading(false);
      }
    };
    const fetchMenu = async () => {
      try {
        const res = await fetch('/api/menu-items');
        const data = await res.json();
        setMenuItems(data);
      } catch (err) {}
    };
    const fetchCategories = async () => {
      try {
        const res = await fetch('/api/categories');
        const data = await res.json();
        setMenuCategories(data);
      } catch (err) {}
    };
    const fetchOrders = async () => {
      try {
        const res = await fetch('/api/orders');
        const data = await res.json();
        const filtered = data.filter((order: Order) => order.tableId.startsWith('online-') || order.tableId.startsWith('takeaway-'));
        setOrders(filtered);
      } catch (err) {
        setOrders([]);
      }
    };
    fetchTables();
    fetchMenu();
    fetchCategories();
    fetchOrders();
    // Polling otomatis untuk update status dapur
    const interval = setInterval(() => {
      fetchTables();
    }, 7000);
    return () => clearInterval(interval);
  }, []);

  const handleTableClick = (table: Table) => {
    // Always allow opening order modal for any table
    setSelectedTable(table);
    setIsOrderModalOpen(true);
  };

  const handleViewOrderDetails = (table: Table) => {
    if (table.orders && table.orders.length > 0) {
      setSelectedOrder(table.orders[0]); // Show the first/latest order
    }
  };

  const handleOnlineOrder = () => {
    setOrderType('online');
    setSelectedTable(null);
    setIsOrderModalOpen(true);
  };
  const handleTakeawayOrder = () => {
    setOrderType('takeaway');
    setSelectedTable(null);
    setIsOrderModalOpen(true);
  };

  const handlePlaceOrder = async (tableId: string, items: OrderItem[], customerData: any) => {
    const total = items.reduce((sum, item) => sum + (item.menuItem.price * item.quantity), 0);
    try {
      const res = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tableId,
          items: items.map(item => ({
            menuItemId: item.menuItem.id,
            quantity: item.quantity,
            notes: item.notes,
            price: item.menuItem.price
          })),
          total,
          ...customerData,
          createdBy: user ? user.username : undefined
        })
      });
      if (!res.ok) {
        let errorMsg = 'Gagal membuat pesanan';
        try {
          const errData = await res.json();
          if (errData && errData.error) errorMsg = errData.error;
        } catch {}
        throw new Error(errorMsg);
      }
      const order = await res.json();
      setTables(prev => prev.map(table =>
        table.id === tableId
          ? {
              ...table,
              status: 'occupied' as const,
              orders: table.orders ? [order, ...table.orders] : [order]
            }
          : table
      ));
      toast({
        title: 'Pesanan berhasil!',
        description: `Pesanan untuk meja ${selectedTable?.number} telah ditempatkan.`,
      });

      // Scroll ke atas setelah pesanan berhasil dibuat
      setTimeout(() => scrollToTop(), 100);
    } catch (err) {
      toast({ title: 'Gagal membuat pesanan', description: String(err), variant: 'destructive' });
    }
  };

  const handlePayOrder = (table: Table, orderId?: string) => {
    setPayingTable(table);
    setSelectedOrderId(orderId || (table.orders && table.orders[0]?.id) || null);
    setIsPaymentModalOpen(true);
  };

  const handlePaymentSuccess = async (tableId: string, orderId?: string, payAllOrders?: boolean) => {
    // Cari table dan orders yang akan dibayar
    const table = tables.find(t => t.id === tableId);

    if (payAllOrders && table?.orders) {
      // Bayar semua pesanan dalam meja
      const orderIds = table.orders.map(order => order.id);

      // Update status semua pesanan menjadi completed
      await Promise.all(
        orderIds.map(id =>
          fetch(`/api/orders/${id}/status`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: 'completed' })
          })
        )
      );

      toast({
        title: 'Pembayaran berhasil',
        description: `${orderIds.length} pesanan telah dibayar. Status meja diubah menjadi tersedia.`
      });
    } else {
      // Bayar pesanan tunggal
      const targetOrderId = orderId || (table?.orders && table.orders[0]?.id);
      if (targetOrderId) {
        await fetch(`/api/orders/${targetOrderId}/status`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status: 'completed' })
        });
        toast({ title: 'Pembayaran berhasil', description: 'Status meja diubah menjadi tersedia.' });
      }
    }

    setIsPaymentModalOpen(false);
    setPayingTable(null);
    setSelectedOrderId(null); // Clear selected order ID after successful payment
    // Refresh data meja
    const res = await fetch('/api/tables');
    const data = await res.json();
    setTables(data);

    // Scroll ke atas setelah pembayaran berhasil
    setTimeout(() => scrollToTop(), 100);
  };

  const handleAddTable = () => {
    setEditingTable(null);
    setIsTableFormOpen(true);
  };

  const handleEditTable = (table: Table) => {
    setEditingTable(table);
    setIsTableFormOpen(true);
  };

  const handleDeleteTable = (table: Table) => {
    setDeletingTable(table);
  };

  const getNextTableNumber = () => {
    if (tables.length === 0) return 1;
    return Math.max(...tables.map(t => t.number)) + 1;
  };

  const handleTableFormSubmit = async (table: Partial<Table>) => {
    try {
      if (editingTable) {
        // Edit
        const res = await fetch(`/api/tables/${editingTable.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(table),
        });
        if (!res.ok) throw new Error('Gagal edit meja');
        const updated = await res.json();
        setTables(prev => prev.map(t => t.id === editingTable.id ? updated : t));
        toast({ title: 'Meja diperbarui', description: `Meja ${updated.number} berhasil diperbarui.` });
      } else {
        // Add
        const newTableData = {
          ...table,
          number: getNextTableNumber(),
        };
        const res = await fetch('/api/tables', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(newTableData),
        });
        if (!res.ok) throw new Error('Gagal tambah meja');
        const created = await res.json();
        setTables(prev => [...prev, created]);
        toast({ title: 'Meja ditambahkan', description: `Meja ${created.number} berhasil ditambahkan.` });

        // Scroll ke atas setelah menambah meja baru
        setTimeout(() => scrollToTop(), 100);
      }
    } catch (err) {
      toast({ title: 'Gagal simpan meja', description: String(err), variant: 'destructive' });
    }
    setIsTableFormOpen(false);
    setEditingTable(null);
  };

  const handleDragEnd = async (result: any) => {
    if (!result.destination) return;
    const reordered = Array.from(tables);
    const [removed] = reordered.splice(result.source.index, 1);
    reordered.splice(result.destination.index, 0, removed);
    setTables(reordered);
    // Optionally, update order in backend here
  };

  const handleConfirmDelete = async () => {
    if (deletingTable) {
      try {
        const res = await fetch(`/api/tables/${deletingTable.id}`, { method: 'DELETE' });
        if (!res.ok) throw new Error('Gagal hapus meja');
        setTables(prev => prev.filter(t => t.id !== deletingTable.id));
        toast({ title: 'Meja dihapus', description: `Meja ${deletingTable.number} berhasil dihapus.` });
      } catch (err) {
        toast({ title: 'Gagal hapus meja', description: String(err), variant: 'destructive' });
      }
      setDeletingTable(null);
    }
  };

  const availableTables = tables.filter(t => t.status === 'available').length;
  const occupiedTables = tables.filter(t => t.status === 'occupied').length;
  const reservedTables = tables.filter(t => t.status === 'reserved').length;

  // Filter meja online/takeaway agar tidak tampil di layout utama
  const visibleTables = tables
    .filter(table => !table.id.startsWith('online-') && !table.id.startsWith('takeaway-'))
    .map(table => {
      if (!table.orders) {
        return { ...table, status: 'available' as 'available' };
      }
      return table;
    })
    .sort((a, b) => a.number - b.number);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      {/* Stats Cards dan Tabs hanya untuk non-PELAYAN */}
      {!isPelayan && (
        <div className="container mx-auto px-6 py-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Meja Tersedia</p>
                  <p className="text-2xl font-bold text-green-600">{availableTables}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Meja Terisi</p>
                  <p className="text-2xl font-bold text-red-600">{occupiedTables}</p>
                </div>
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Reservasi</p>
                  <p className="text-2xl font-bold text-yellow-600">{reservedTables}</p>
                </div>
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Meja</p>
                  <p className="text-2xl font-bold text-gray-800">{tables.length}</p>
                </div>
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  <Store className="w-6 h-6 text-gray-600" />
                </div>
              </div>
            </div>
          </div>
          {/* Tables Section dengan tab */}
          <div className="mb-4">
            <div className="tabs flex items-center gap-2 bg-white border-b border-gray-200 px-6 rounded-t-2xl shadow-sm overflow-hidden">
              <button
                className={`tab px-6 py-3 text-base font-semibold rounded-t-2xl transition-all duration-200 border-b-4 border-transparent focus:outline-none ${tab === 'layout' ? 'active text-blue-600 border-blue-500 bg-gradient-to-b from-blue-50 to-white shadow' : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50'}`}
                onClick={() => setTab('layout')}
              >
                Layout Meja
              </button>
              <button
                className={`tab px-6 py-3 text-base font-semibold rounded-t-2xl transition-all duration-200 border-b-4 border-transparent focus:outline-none ${tab === 'online' ? 'active text-blue-600 border-blue-500 bg-gradient-to-b from-blue-50 to-white shadow' : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50'}`}
                onClick={() => setTab('online')}
              >
                Pesanan Online/Take Away
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Layout meja untuk semua role, tapi hanya admin yang bisa edit/delete/tambah meja */}
      <div className="container mx-auto px-6 py-6" ref={tableContainerRef}>
        <div className="bg-white p-4 rounded shadow">
          {tab === 'layout' && (
            <>
              {isAdmin && (
                <div className="mb-4 flex justify-end">
                  <button
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition"
                    onClick={handleAddTable}
                  >
                    Tambah Meja
                  </button>
                </div>
              )}
              {loading ? (
                <div className="text-center py-12 text-gray-500">Memuat data meja...</div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                  {visibleTables.map((table) => (
                    <TableCard
                      key={table.id}
                      table={table}
                      onTableClick={handleTableClick}
                      onViewOrderDetails={handleViewOrderDetails}
                      onEditTable={isAdmin ? handleEditTable : undefined}
                      onDeleteTable={isAdmin ? handleDeleteTable : undefined}
                      onPayOrder={isAdmin ? handlePayOrder : undefined}
                    />
                  ))}
                </div>
              )}
            </>
          )}
          {tab === 'online' ? (
            <>
              <div className="flex gap-2 mb-4">
                <button
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition"
                  onClick={handleOnlineOrder}
                >
                  Pesanan Online
                </button>
                <button
                  className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition"
                  onClick={handleTakeawayOrder}
                >
                  Take Away
                </button>
              </div>
              {orders.length === 0 ? (
                <div>Tidak ada pesanan online/takeaway.</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full border">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="px-4 py-2 border">Jenis</th>
                        <th className="px-4 py-2 border">ID Pesanan</th>
                        <th className="px-4 py-2 border">Customer</th>
                        <th className="px-4 py-2 border">Status</th>
                        <th className="px-4 py-2 border">Total</th>
                        <th className="px-4 py-2 border">Waktu</th>
                        <th className="px-4 py-2 border">Pembayaran</th>
                      </tr>
                    </thead>
                    <tbody>
                      {orders.filter(order => order.status.toLowerCase() !== 'completed').map(order => (
                        <tr key={order.id} className="cursor-pointer hover:bg-blue-50" onClick={() => setSelectedOrder(order)}>
                          <td className="px-4 py-2 border text-center font-semibold">
                            {order.tableId.startsWith('online-') ? 'Online' : 'Take Away'}
                          </td>
                          <td className="px-4 py-2 border">{order.noStruk}</td>
                          <td className="px-4 py-2 border">{order.customer?.name || '-'}</td>
                          <td className="px-4 py-2 border capitalize">{order.status}</td>
                          <td className="px-4 py-2 border">Rp{order.total.toLocaleString('id-ID')}</td>
                          <td className="px-4 py-2 border">{new Date(order.createdAt).toLocaleString('id-ID')}</td>
                          <td className="px-4 py-2 border">
                            <button
                              className="bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow transition"
                              onClick={e => { e.stopPropagation(); handlePayOrder({ id: order.tableId, orders: [order] } as any, order.id); }}
                            >
                              Pembayaran
                            </button>
                            {order.createdBy && (
                              <div className="text-xs text-gray-500 mt-1">Diinput oleh: <span className="font-semibold">{order.createdBy}</span></div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          ) : null}
        </div>
      </div>
      {/* Modal hanya untuk admin */}
      {isAdmin && (
        <>
          <TableFormModal
            open={isTableFormOpen}
            onClose={() => { setIsTableFormOpen(false); setEditingTable(null); }}
            onSubmit={handleTableFormSubmit}
            initialData={editingTable}
            nextTableNumber={getNextTableNumber()}
          />
          <ConfirmDialog
            open={!!deletingTable}
            onClose={() => setDeletingTable(null)}
            onConfirm={handleConfirmDelete}
            title="Hapus Meja?"
            description={`Yakin ingin menghapus meja ${deletingTable?.number}?`}
          />
          <PaymentModal
            open={isPaymentModalOpen}
            table={payingTable}
            orderId={selectedOrderId || undefined}
            onClose={() => setIsPaymentModalOpen(false)}
            onSuccess={handlePaymentSuccess}
          />
          {selectedOrder && (
            <OrderDetailModal
              order={selectedOrder}
              onClose={() => setSelectedOrder(null)}
            />
          )}
        </>
      )}
      {(isPelayan || !isPelayan) && (
        <OrderModal
          table={selectedTable!}
          isOpen={isOrderModalOpen}
          onClose={() => setIsOrderModalOpen(false)}
          onPlaceOrder={handlePlaceOrder}
          onPayOrder={!isPelayan ? (table, orderId) => handlePayOrder(table, orderId) : undefined}
          orderType={orderType}
          menuItems={menuItems}
          menuCategories={menuCategories}
        />
      )}
    </div>
  );
};

export default Index;
