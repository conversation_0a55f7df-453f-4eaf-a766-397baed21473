
import { useState, useEffect, useRef } from 'react';
import { Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { Order } from '../types/pos';
import { formatCurrency } from '../utils/posData';
import { useToast } from '../hooks/use-toast';
import Navigation from '../components/Navigation';

const Kitchen = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const { toast } = useToast();
  const prevOrderIds = useRef<string[]>([]);
  const notificationSound = useRef<HTMLAudioElement | null>(null);
  if (!notificationSound.current) {
    notificationSound.current = new window.Audio('/mixkit-happy-bells-notification-937.wav');
  }

  // Ambil role user dari localStorage
  const userStr = localStorage.getItem('user');
  const user = userStr ? JSON.parse(userStr) : null;

  // Fetch orders from backend
  const fetchOrders = () => {
    fetch('/api/orders')
      .then(res => res.json())
      .then(data => {
        setOrders(data.map((order: any) => ({
          ...order,
          createdAt: new Date(order.createdAt),
          updatedAt: new Date(order.updatedAt)
        })));
      });
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  useEffect(() => {
    // Unlock audio pada interaksi pertama user, hanya jika role KOKI
    if (user && user.role === 'KOKI') {
      const unlockAudio = () => {
        if (notificationSound.current) {
          notificationSound.current.play().catch(() => {});
        }
        window.removeEventListener('click', unlockAudio);
      };
      window.addEventListener('click', unlockAudio);
      return () => window.removeEventListener('click', unlockAudio);
    }
  }, [user]);

  useEffect(() => {
    // Notifikasi suara jika ada pesanan baru, hanya untuk role KOKI
    if (user && user.role === 'KOKI') {
      const currentIds = orders.map(order => order.id);
      const prevIds = prevOrderIds.current;
      const newOrder = currentIds.find(id => !prevIds.includes(id));
      if (newOrder && notificationSound.current) {
        notificationSound.current.play();
      }
      prevOrderIds.current = currentIds;
    }
  }, [orders, user]);

  const handleStatusChange = async (orderId: string, newStatus: Order['status']) => {
    try {
      await fetch(`/api/orders/${orderId}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });
      fetchOrders();
      toast({
        title: "Status pesanan diubah",
        description: `Pesanan ${orderId} sekarang ${newStatus}`,
      });
    } catch (err) {
      toast({
        title: 'Gagal update status',
        description: String(err),
        variant: 'destructive'
      });
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'preparing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ready': return 'bg-green-100 text-green-800 border-green-200';
      case 'served': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getElapsedTime = (createdAt: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60));
    return diffInMinutes;
  };

  // Only show orders with status pending, preparing, or ready
  const visibleOrders = orders.filter(order => ['pending', 'preparing', 'ready'].includes(order.status));
  const pendingOrders = visibleOrders.filter(order => order.status === 'pending');
  const preparingOrders = visibleOrders.filter(order => order.status === 'preparing');
  const readyOrders = visibleOrders.filter(order => order.status === 'ready');

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-6 py-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Dapur</h1>
          <p className="text-gray-600">Kelola pesanan masuk dan status persiapan</p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pesanan Menunggu</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingOrders.length}</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Sedang Dimasak</p>
                <p className="text-2xl font-bold text-blue-600">{preparingOrders.length}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <AlertCircle className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Siap Disajikan</p>
                <p className="text-2xl font-bold text-green-600">{readyOrders.length}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Orders */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Pending Orders */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-4 border-b">
              <h3 className="font-semibold text-gray-800">Pesanan Baru</h3>
            </div>
            <div className="p-4 space-y-4">
              {pendingOrders.map(order => (
                <div key={order.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">
                        {order.tableId === 'online-order' ? 'Online Order' : order.tableId === 'takeaway' ? 'Take Away' : `Meja ${order.tableId}`}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {getElapsedTime(order.createdAt)} menit yang lalu
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    {(order.orderItems || []).map(item => (
                      <div key={item.id} className="flex justify-between">
                        <div>
                          <span className="font-medium">{item.quantity}x {item.menuItem.name}</span>
                          {item.notes && (
                            <p className="text-sm text-gray-600 italic">Note: {item.notes}</p>
                          )}
                        </div>
                      </div>
                    ))}
                    {order.createdBy && (
                      <div className="text-xs text-gray-500">Diinput oleh: <span className="font-semibold">{order.createdBy}</span></div>
                    )}
                  </div>
                  
                  <button
                    onClick={() => handleStatusChange(order.id, 'preparing')}
                    className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Mulai Masak
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Preparing Orders */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-4 border-b">
              <h3 className="font-semibold text-gray-800">Sedang Dimasak</h3>
            </div>
            <div className="p-4 space-y-4">
              {preparingOrders.map(order => (
                <div key={order.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">
                        {order.tableId === 'online-order' ? 'Online Order' : order.tableId === 'takeaway' ? 'Take Away' : `Meja ${order.tableId}`}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {getElapsedTime(order.createdAt)} menit yang lalu
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    {(order.orderItems || []).map(item => (
                      <div key={item.id} className="flex justify-between">
                        <div>
                          <span className="font-medium">{item.quantity}x {item.menuItem.name}</span>
                          {item.notes && (
                            <p className="text-sm text-gray-600 italic">Note: {item.notes}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <button
                    onClick={() => handleStatusChange(order.id, 'ready')}
                    className="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Siap Disajikan
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Ready Orders */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-4 border-b">
              <h3 className="font-semibold text-gray-800">Siap Disajikan</h3>
            </div>
            <div className="p-4 space-y-4">
              {readyOrders.map(order => (
                <div key={order.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">
                        {order.tableId === 'online-order' ? 'Online Order' : order.tableId === 'takeaway' ? 'Take Away' : `Meja ${order.tableId}`}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {getElapsedTime(order.createdAt)} menit yang lalu
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    {(order.orderItems || []).map(item => (
                      <div key={item.id} className="flex justify-between">
                        <div>
                          <span className="font-medium">{item.quantity}x {item.menuItem.name}</span>
                          {item.notes && (
                            <p className="text-sm text-gray-600 italic">Note: {item.notes}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <button
                    onClick={() => handleStatusChange(order.id, 'served')}
                    className="w-full bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    Sudah Disajikan
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Kitchen;
