import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function main() {
  // Kategori
  const makanan = await prisma.menuCategory.create({
    data: { name: '<PERSON><PERSON><PERSON>', icon: '🍽️', color: '#4ade80' }
  });
  const minuman = await prisma.menuCategory.create({
    data: { name: 'Minuman', icon: '🥤', color: '#60a5fa' }
  });
  const dessert = await prisma.menuCategory.create({
    data: { name: 'Dessert', icon: '🍰', color: '#fbbf24' }
  });
  const appetizer = await prisma.menuCategory.create({
    data: { name: 'Appetizer', icon: '🥗', color: '#a78bfa' }
  });

  // Menu
  await prisma.menuItem.createMany({
    data: [
      { name: 'Nasi Goreng Spesial', price: 25000, categoryId: makanan.id },
      { name: '<PERSON><PERSON>', price: 35000, categoryId: makanan.id },
      { name: 'Gado-Gado', price: 20000, categoryId: makanan.id },
      { name: '<PERSON><PERSON> <PERSON><PERSON>', price: 5000, categoryId: minuman.id },
      { name: 'Jus Jeruk', price: 10000, categoryId: minuman.id },
      { name: 'Pudding Coklat', price: 12000, categoryId: dessert.id },
      { name: 'Salad Buah', price: 15000, categoryId: appetizer.id }
    ]
  });

  // Meja
  await prisma.table.createMany({
    data: [
      { number: 1, seats: 4 },
      { number: 2, seats: 4 },
      { number: 3, seats: 6 },
      { number: 4, seats: 2 },
      { number: 5, seats: 2 },
      { number: 6, seats: 8 },
      { number: 7, seats: 4 }
    ]
  });

  // User
  await prisma.user.createMany({
    data: [
      {
        name: 'Admin',
        username: 'admin',
        email: '<EMAIL>',
        password: 'password123',
        role: 'ADMIN',
      },
      {
        name: 'Kasir',
        username: 'kasir',
        email: '<EMAIL>',
        password: 'password123',
        role: 'KASIR',
      },
      {
        name: 'Koki',
        username: 'koki',
        email: '<EMAIL>',
        password: 'password123',
        role: 'KOKI',
      },
      {
        name: 'Pelayan',
        username: 'pelayan',
        email: '<EMAIL>',
        password: 'password123',
        role: 'PELAYAN',
      },
    ]
  });
}

main()
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 