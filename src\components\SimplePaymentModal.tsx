import { useEffect, useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Table } from '../types/pos';
import { formatCurrency } from '../utils/posData';

interface SimplePaymentModalProps {
  open: boolean;
  table: Table | null;
  orderId?: string;
  onClose: () => void;
  onSuccess: (tableId: string, orderId?: string, payAllOrders?: boolean) => void;
}

const SimplePaymentModal = ({ open, table, orderId, onClose, onSuccess }: SimplePaymentModalProps) => {
  const [amount, setAmount] = useState('');
  const [error, setError] = useState('');
  const [selectedMethod, setSelectedMethod] = useState<'cash' | 'card' | 'qr'>('cash');
  const [payAllOrders, setPayAllOrders] = useState(false);

  useEffect(() => {
    if (open) {
      setAmount(''); // Empty by default
      setError('');
      setPayAllOrders(table?.orders && table.orders.length > 1);
    }
  }, [open, table]);

  // Tentukan order yang akan dibayar
  const selectedOrder = orderId && table?.orders ? table.orders.find(o => o.id === orderId) : (table?.orders && table.orders[0]);

  // Hitung total berdasarkan mode pembayaran
  const allOrders = table?.orders || [];
  const ordersToProcess = payAllOrders ? allOrders : (selectedOrder ? [selectedOrder] : []);
  const total = ordersToProcess.reduce((sum, order) => sum + order.total, 0);

  if (!table || ordersToProcess.length === 0) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedMethod === 'cash' && Number(amount) < total) {
      setError('Jumlah bayar kurang dari total.');
      return;
    }
    setError('');
    onSuccess(table.id, orderId, payAllOrders);
  };

  const getChange = () => {
    if (selectedMethod !== 'cash') return 0;
    const received = parseFloat(amount) || 0;
    return Math.max(0, received - total);
  };

  const getTableDisplayName = () => {
    if (table.id.startsWith('online-order')) return `Online Order`;
    if (table.id.startsWith('takeaway')) return `Take Away`;
    return `Meja ${table.id}`;
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-[95vw] max-h-[90vh] p-0 bg-gradient-to-br from-indigo-400 via-purple-400 to-blue-300 border-0 shadow-2xl rounded-2xl overflow-hidden">
        <div className="w-full h-full bg-white/90 backdrop-blur-lg rounded-2xl p-6 flex flex-col gap-6 overflow-y-auto">
          {/* Header */}
          <div className="text-center mb-4">
            <h1 className="text-3xl font-extrabold text-gray-800 flex items-center justify-center gap-2">
              <span role='img' aria-label='dining'>🍽️</span> Sistem Pembayaran Modern
            </h1>
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          {/* Main Content - 2 Column Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1">
            {/* Left Column - Order Details */}
            <div className="space-y-4">
              {/* Order Details Card */}
              <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20">
                <div className="flex items-center gap-2 mb-3">
                  <span role='img' aria-label='receipt'>📋</span>
                  <h3 className="font-bold text-gray-800">Detail Pesanan</h3>
                </div>

                <div className="space-y-2">
                  {ordersToProcess.map(order =>
                    order.orderItems?.map(item => (
                      <div key={item.id} className="flex justify-between text-sm">
                        <span className="text-gray-700">{item.quantity}x {item.menuItem.name}</span>
                        <span className="font-semibold">Rp {(item.menuItem.price * item.quantity).toLocaleString('id-ID')}</span>
                      </div>
                    ))
                  )}
                </div>

                {/* Discount and Tax */}
                <div className="mt-3 pt-3 border-t border-gray-200 space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Diskon (10%)</span>
                    <span className="text-green-600 font-semibold">-Rp 3.500</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Pajak (11%)</span>
                    <span className="text-red-600 font-semibold">Rp 3.850</span>
                  </div>
                </div>
              </div>

              {/* Total Payment Card */}
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white shadow-lg">
                <div className="text-center">
                  <p className="text-blue-100 text-sm mb-1">Total Pembayaran</p>
                  <p className="text-3xl font-bold">Rp {total.toLocaleString('id-ID')}</p>
                </div>
              </div>
            </div>

            {/* Right Column - Payment Methods */}
            <div className="space-y-4">
              {/* Payment Methods Card */}
              <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20">
                <div className="flex items-center gap-2 mb-4">
                  <span role='img' aria-label='diamond'>💎💎</span>
                  <h3 className="font-bold text-gray-800">Metode Pembayaran</h3>
                </div>

                <div className="grid grid-cols-3 gap-3">
                  <button
                    type="button"
                    onClick={() => setSelectedMethod('cash')}
                    className={`p-4 rounded-xl border-2 transition-all flex flex-col items-center gap-2 ${
                      selectedMethod === 'cash'
                        ? 'border-blue-500 bg-blue-100'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <span className="text-2xl">🔥</span>
                    <span className="text-sm font-semibold">Tunai</span>
                  </button>

                  <button
                    type="button"
                    onClick={() => setSelectedMethod('card')}
                    className={`p-4 rounded-xl border-2 transition-all flex flex-col items-center gap-2 ${
                      selectedMethod === 'card'
                        ? 'border-blue-500 bg-blue-100'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <span className="text-2xl">💳</span>
                    <span className="text-sm font-semibold">Kartu</span>
                  </button>

                  <button
                    type="button"
                    onClick={() => setSelectedMethod('qr')}
                    className={`p-4 rounded-xl border-2 transition-all flex flex-col items-center gap-2 ${
                      selectedMethod === 'qr'
                        ? 'border-blue-500 bg-blue-100'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <span className="text-2xl">📱</span>
                    <span className="text-sm font-semibold">QR Code</span>
                  </button>
                </div>
              </div>

              {/* Cash Input */}
              {selectedMethod === 'cash' && (
                <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20">
                  <div className="flex items-center gap-2 mb-4">
                    <span role='img' aria-label='money'>💚</span>
                    <h3 className="font-bold text-gray-800">Uang Diterima</h3>
                  </div>

                  {/* Amount Display */}
                  <div className="bg-gray-100 border border-gray-300 rounded-xl p-4 text-4xl font-bold text-center text-gray-800 mb-4">
                    {amount ? parseInt(amount).toLocaleString('id-ID') : '0'}
                  </div>

                  {/* Denomination Buttons */}
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    {[5000, 10000, 20000, 50000, 100000, 200000].map(denomination => (
                      <button
                        type="button"
                        key={denomination}
                        className="bg-gray-200 hover:bg-gray-300 text-sm font-bold rounded-lg py-3 px-2 transition-all duration-150"
                        onClick={() => setAmount(prev => (parseInt(prev || '0') + denomination).toString())}
                      >
                        {denomination.toLocaleString('id-ID')}
                      </button>
                    ))}
                  </div>

                  {/* Clear Button */}
                  <button
                    type="button"
                    onClick={() => setAmount('')}
                    className="w-full bg-red-100 hover:bg-red-200 text-red-700 font-bold rounded-lg py-3 mb-4 transition-all duration-150"
                  >
                    Hapus / Clear
                  </button>

                  {/* Change Display */}
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="text-center">
                      <p className="text-red-600 font-semibold mb-1">Kembalian</p>
                      <p className="text-2xl font-bold text-red-700">Rp {getChange().toLocaleString('id-ID')}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-200">
              {error}
            </div>
          )}

          {/* Submit Button */}
          <button
            onClick={handleSubmit}
            disabled={selectedMethod === 'cash' && (!amount || parseFloat(amount) < total)}
            className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed text-white py-4 rounded-xl font-bold text-lg transition-all duration-200 shadow-lg flex items-center justify-center gap-2"
          >
            <span role='img' aria-label='sparkles'>✨</span>
            Proses Pembayaran
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SimplePaymentModal;
