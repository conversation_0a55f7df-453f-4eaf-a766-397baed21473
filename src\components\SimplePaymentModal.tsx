import { useEffect, useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Table } from '../types/pos';
import { formatCurrency } from '../utils/posData';

interface SimplePaymentModalProps {
  open: boolean;
  table: Table | null;
  orderId?: string;
  onClose: () => void;
  onSuccess: (tableId: string, orderId?: string, payAllOrders?: boolean) => void;
}

const SimplePaymentModal = ({ open, table, orderId, onClose, onSuccess }: SimplePaymentModalProps) => {
  const [amount, setAmount] = useState('');
  const [error, setError] = useState('');
  const [selectedMethod, setSelectedMethod] = useState<'cash' | 'card' | 'qr'>('cash');
  const [payAllOrders, setPayAllOrders] = useState(false);

  useEffect(() => {
    if (open) {
      setAmount('10000'); // Default amount
      setError('');
      setPayAllOrders(table?.orders && table.orders.length > 1);
    }
  }, [open, table]);

  // Tentukan order yang akan dibayar
  const selectedOrder = orderId && table?.orders ? table.orders.find(o => o.id === orderId) : (table?.orders && table.orders[0]);

  // Hitung total berdasarkan mode pembayaran
  const allOrders = table?.orders || [];
  const ordersToProcess = payAllOrders ? allOrders : (selectedOrder ? [selectedOrder] : []);
  const total = ordersToProcess.reduce((sum, order) => sum + order.total, 0);

  if (!table || ordersToProcess.length === 0) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedMethod === 'cash' && Number(amount) < total) {
      setError('Jumlah bayar kurang dari total.');
      return;
    }
    setError('');
    onSuccess(table.id, orderId, payAllOrders);
  };

  const getChange = () => {
    if (selectedMethod !== 'cash') return 0;
    const received = parseFloat(amount) || 0;
    return Math.max(0, received - total);
  };

  const getTableDisplayName = () => {
    if (table.id.startsWith('online-order')) return `Online Order`;
    if (table.id.startsWith('takeaway')) return `Take Away`;
    return `Meja ${table.id}`;
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-lg w-[95vw] max-h-[90vh] p-0 border-0 shadow-xl rounded-lg overflow-hidden">
        <div className="bg-white rounded-lg">
          {/* Header */}
          <div className="p-4 border-b flex justify-between items-center">
            <DialogTitle className="text-lg font-semibold text-gray-800">
              Pembayaran
            </DialogTitle>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl font-bold"
            >
              ×
            </button>
          </div>

          <div className="p-4 space-y-4">
            {/* Order Details */}
            <div>
              <h3 className="font-medium text-gray-800 mb-2">
                {getTableDisplayName()}
              </h3>
              <div className="space-y-1">
                {ordersToProcess.map(order => 
                  order.orderItems?.map(item => (
                    <div key={item.id} className="flex justify-between text-sm">
                      <span>{item.quantity}x {item.menuItem.name}</span>
                      <span>Rp {(item.menuItem.price * item.quantity).toLocaleString('id-ID')}</span>
                    </div>
                  ))
                )}
              </div>
              
              <div className="flex justify-between font-semibold text-lg mt-3 pt-3 border-t">
                <span>Total:</span>
                <span>Rp {total.toLocaleString('id-ID')}</span>
              </div>
            </div>

            {/* Payment Methods */}
            <div>
              <h4 className="font-medium mb-3">Metode Pembayaran</h4>
              <div className="grid grid-cols-3 gap-3">
                <button
                  type="button"
                  onClick={() => setSelectedMethod('cash')}
                  className={`p-4 rounded-lg border-2 transition-all flex flex-col items-center gap-2 ${
                    selectedMethod === 'cash'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <span className="text-2xl">🔥</span>
                  <span className="text-sm font-medium">Tunai</span>
                </button>

                <button
                  type="button"
                  onClick={() => setSelectedMethod('card')}
                  className={`p-4 rounded-lg border-2 transition-all flex flex-col items-center gap-2 ${
                    selectedMethod === 'card'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <span className="text-2xl">💳</span>
                  <span className="text-sm font-medium">Kartu</span>
                </button>

                <button
                  type="button"
                  onClick={() => setSelectedMethod('qr')}
                  className={`p-4 rounded-lg border-2 transition-all flex flex-col items-center gap-2 ${
                    selectedMethod === 'qr'
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <span className="text-2xl">📱</span>
                  <span className="text-sm font-medium">QR Code</span>
                </button>
              </div>
            </div>

            {/* Cash Input */}
            {selectedMethod === 'cash' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Uang Diterima
                </label>

                {/* Amount Display */}
                <div className="bg-gray-100 border border-gray-300 rounded-lg p-4 text-2xl font-bold text-center text-gray-800 mb-4">
                  Rp {parseInt(amount || '10000').toLocaleString('id-ID')}
                </div>

                {/* Denomination Buttons */}
                <div className="grid grid-cols-3 gap-3 mb-4">
                  {[5000, 10000, 20000, 50000, 100000, 200000].map(denomination => (
                    <button
                      type="button"
                      key={denomination}
                      className="bg-gray-200 hover:bg-gray-300 text-sm font-semibold rounded-lg py-3 px-2 transition-all duration-150"
                      onClick={() => setAmount(prev => (parseInt(prev || '0') + denomination).toString())}
                    >
                      {denomination.toLocaleString('id-ID')}
                    </button>
                  ))}
                </div>

                {/* Clear Button */}
                <button
                  type="button"
                  onClick={() => setAmount('')}
                  className="w-full bg-red-100 hover:bg-red-200 text-red-700 font-semibold rounded-lg py-3 mb-4 text-sm transition-all duration-150"
                >
                  Hapus / Clear
                </button>

                {/* Manual Input */}
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="10000"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mb-4 text-center"
                />

                {/* Change Display */}
                <div className={`p-3 rounded-lg mb-4 ${
                  parseFloat(amount || '0') >= total ? 'bg-red-50 border border-red-200' : 'bg-red-50 border border-red-200'
                }`}>
                  <div className="flex justify-between">
                    <span className="text-sm text-red-600 font-medium">
                      Kembalian:
                    </span>
                    <span className="font-semibold text-red-700">
                      Rp {getChange().toLocaleString('id-ID')}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Card Input */}
            {selectedMethod === 'card' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nomor Kartu
                </label>
                <input
                  type="text"
                  placeholder="Masukkan nomor kartu"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mb-3"
                />
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload Bukti Pembayaran (Opsional)
                </label>
                <input
                  type="file"
                  accept="image/*"
                  className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            )}

            {/* QR Code Input */}
            {selectedMethod === 'qr' && (
              <div>
                <div className="text-center mb-3">
                  <div className="text-sm text-gray-600 mb-2">Scan QR Code untuk pembayaran</div>
                  <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8">
                    <span className="text-4xl">📱</span>
                    <div className="text-sm text-gray-500 mt-2">QR Code akan ditampilkan di sini</div>
                  </div>
                </div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload Bukti Pembayaran (Opsional)
                </label>
                <input
                  type="file"
                  accept="image/*"
                  className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
                {error}
              </div>
            )}

            {/* Submit Button */}
            <button
              onClick={handleSubmit}
              disabled={selectedMethod === 'cash' && (!amount || parseFloat(amount) < total)}
              className="w-full bg-gray-400 hover:bg-gray-500 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-4 rounded-lg font-semibold transition-colors text-lg"
            >
              Proses Pembayaran
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SimplePaymentModal;
